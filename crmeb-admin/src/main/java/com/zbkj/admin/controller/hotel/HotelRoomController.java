package com.zbkj.admin.controller.hotel;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomBatchSpecialOfferRequest;
import com.zbkj.common.request.hotel.HotelRoomBatchWeekendRequest;
import com.zbkj.common.request.hotel.HotelRoomRequest;
import com.zbkj.common.request.hotel.HotelRoomSearchRequest;
import com.zbkj.common.request.hotel.HotelRoomWithPricesRequest;
import com.zbkj.common.response.hotel.HotelRoomResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.common.model.hotel.HotelRoom;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.service.hotel.HotelRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒店房型管理控制器 - 商户端
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("api/admin/hotel/room")
@Api(tags = "商户端 - 酒店房型管理")
@Validated
public class HotelRoomController {

    @Autowired
    private HotelRoomService hotelRoomService;

    /**
     * 分页查询房型列表
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:list')")
    @ApiOperation(value = "分页查询房型列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<PageInfo<HotelRoomResponse>> getList(@Validated HotelRoomSearchRequest request,
                                                             @Validated PageParamRequest pageParamRequest) {
        PageInfo<HotelRoomResponse> pageInfo = hotelRoomService.getMerchantPage(request, pageParamRequest);
        return CommonResult.success(pageInfo);
    }

    /**
     * 新增房型
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:save')")
    @ApiOperation(value = "新增房型")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Valid HotelRoomRequest request) {
        if (hotelRoomService.saveHotelRoom(request)) {
            return CommonResult.success("新增房型成功");
        } else {
            return CommonResult.failed("新增房型失败");
        }
    }

    /**
     * 新增房型和价格策略
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:save')")
    @ApiOperation(value = "新增房型和价格策略")
    @RequestMapping(value = "/saveWithPrices", method = RequestMethod.POST)
    public CommonResult<String> saveWithPrices(@RequestBody @Valid HotelRoomWithPricesRequest request) {
        if (hotelRoomService.saveHotelRoomWithPrices(request)) {
            return CommonResult.success("新增房型和价格策略成功");
        } else {
            return CommonResult.failed("新增房型和价格策略失败");
        }
    }

    /**
     * 修改房型
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:update')")
    @ApiOperation(value = "修改房型")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Valid HotelRoomRequest request) {
        if (hotelRoomService.updateHotelRoom(request)) {
            return CommonResult.success("修改房型成功");
        } else {
            return CommonResult.failed("修改房型失败");
        }
    }

    /**
     * 获取房型详情
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:info')")
    @ApiOperation(value = "获取房型详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<HotelRoomResponse> info(@PathVariable @NotNull(message = "房型ID不能为空") Integer id) {
        HotelRoomResponse response = hotelRoomService.getInfo(id);
        return CommonResult.success(response);
    }

    /**
     * 删除房型
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:delete')")
    @ApiOperation(value = "删除房型")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@PathVariable @NotNull(message = "房型ID不能为空") Integer id) {
        if (hotelRoomService.delete(id)) {
            return CommonResult.success("删除房型成功");
        } else {
            return CommonResult.failed("删除房型失败");
        }
    }

    /**
     * 修改房型状态
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:status')")
    @ApiOperation(value = "修改房型状态")
    @RequestMapping(value = "/status/{id}/{status}", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@PathVariable @NotNull(message = "房型ID不能为空") Integer id,
                                             @PathVariable @NotNull(message = "状态不能为空") Integer status) {
        if (status != 0 && status != 1) {
            return CommonResult.failed("状态值错误");
        }

        if (hotelRoomService.updateRoomStatus(id, status)) {
            return CommonResult.success("修改状态成功");
        } else {
            return CommonResult.failed("修改状态失败");
        }
    }

    /**
     * 修改房型库存
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:stock')")
    @ApiOperation(value = "修改房型库存")
    @RequestMapping(value = "/stock/{id}/{totalRooms}", method = RequestMethod.POST)
    public CommonResult<String> updateStock(@PathVariable @NotNull(message = "房型ID不能为空") Integer id,
                                            @PathVariable @NotNull(message = "总房间数不能为空") Integer totalRooms) {
        if (totalRooms < 0) {
            return CommonResult.failed("总房间数不能小于0");
        }

        if (hotelRoomService.updateRoomStock(id, totalRooms)) {
            return CommonResult.success("修改库存成功");
        } else {
            return CommonResult.failed("修改库存失败");
        }
    }

    /**
     * 批量设置特价房源
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:special')")
    @ApiOperation(value = "批量设置特价房源")
    @RequestMapping(value = "/batchSetSpecialOffer", method = RequestMethod.POST)
    public CommonResult<String> batchSetSpecialOffer(@RequestBody @Valid HotelRoomBatchSpecialOfferRequest request) {
        if (hotelRoomService.batchSetSpecialOffer(request.getRoomIds(), request.getIsSpecialOffer())) {
            String action = request.getIsSpecialOffer() == 1 ? "设为特价房源" : "取消特价";
            return CommonResult.success(action + "成功");
        } else {
            return CommonResult.failed("操作失败");
        }
    }

    /**
     * 批量设置周末不加价
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:weekend')")
    @ApiOperation(value = "批量设置周末不加价")
    @RequestMapping(value = "/batchSetWeekendNoMarkup", method = RequestMethod.POST)
    public CommonResult<String> batchSetWeekendNoMarkup(@RequestBody @Valid HotelRoomBatchWeekendRequest request) {
        if (hotelRoomService.batchSetWeekendNoMarkup(request.getRoomIds(), request.getWeekendNoMarkup())) {
            String action = request.getWeekendNoMarkup() == 1 ? "开启周末不加价" : "关闭周末不加价";
            return CommonResult.success(action + "成功");
        } else {
            return CommonResult.failed("操作失败");
        }
    }

    /**
     * 获取特价房源列表
     */
//    @PreAuthorize("hasAuthority('admin:hotel:room:list')")
    @ApiOperation(value = "获取特价房源列表")
    @RequestMapping(value = "/specialOfferList", method = RequestMethod.GET)
    public CommonResult<List<HotelRoomResponse>> getSpecialOfferList() {
        Integer merchantId = SecurityUtil.getLoginUserVo().getUser().getMerId();
        List<HotelRoom> rooms = hotelRoomService.getSpecialOfferList(merchantId);
        List<HotelRoomResponse> responses = rooms.stream()
            .map(this::convertToResponse)
            .collect(Collectors.toList());
        return CommonResult.success(responses);
    }

    /**
     * 转换为响应对象
     */
    private HotelRoomResponse convertToResponse(HotelRoom room) {
        HotelRoomResponse response = new HotelRoomResponse();
        response.setId(room.getId());
        response.setMerId(room.getMerId());
        response.setRoomName(room.getRoomName());
        response.setRoomType(room.getRoomType());
        response.setBedType(room.getBedType());
        response.setMaxGuests(room.getMaxGuests());
        response.setRoomArea(room.getRoomArea());
        response.setRoomImages(room.getRoomImages());
        response.setRoomDescription(room.getRoomDescription());
        response.setTotalRooms(room.getTotalRooms());
        response.setStatus(room.getStatus());
        response.setSort(room.getSort());
        response.setIsSpecialOffer(room.getIsSpecialOffer());
        response.setWeekendNoMarkup(room.getWeekendNoMarkup());
        response.setCreateTime(room.getCreateTime());
        response.setUpdateTime(room.getUpdateTime());
        return response;
    }
}
