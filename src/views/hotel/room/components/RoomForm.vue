<template>
  <el-dialog
    :title="isEdit ? '编辑房型' : (formData.id ? '查看房型' : '新增房型')"
    :visible="visible"
    width="900px"
    append-to-body
    @close="handleClose"
  >
    <!-- 步骤条 - 仅在新增时显示 -->
    <el-steps v-if="!isEdit && !formData.id" :active="currentStep" finish-status="success" style="margin-bottom: 20px;">
      <el-step title="房型信息" description="填写房型基本信息"></el-step>
      <el-step title="价格策略" description="配置价格策略"></el-step>
    </el-steps>

    <!-- 第一步：房型基本信息 -->
    <div v-if="currentStep === 0 || isEdit || formData.id">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        :disabled="!isEdit && formData.id"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房型名称" prop="roomName">
              <el-input v-model="form.roomName" placeholder="请输入房型名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房型类型" prop="roomType">
              <el-select v-model="form.roomType" placeholder="请选择房型类型" style="width: 100%">
                <el-option label="标准间" value="标准间"/>
                <el-option label="大床房" value="大床房"/>
                <el-option label="套房" value="套房"/>
                <el-option label="豪华间" value="豪华间"/>
                <el-option label="商务间" value="商务间"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="床型" prop="bedType">
              <el-select v-model="form.bedType" placeholder="请选择床型" style="width: 100%">
                <el-option label="单人床" value="单人床"/>
                <el-option label="双人床" value="双人床"/>
                <el-option label="大床" value="大床"/>
                <el-option label="双床" value="双床"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大入住人数" prop="maxGuests">
              <el-input-number
                v-model="form.maxGuests"
                :min="1"
                :max="10"
                placeholder="请输入最大入住人数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房型基础价格" prop="basePrice">
              <el-input-number
                v-model="form.basePrice"
                :min="0.01"
                :max="99999.99"
                :precision="2"
                placeholder="请输入基础价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总房间数" prop="totalRooms">
              <el-input-number
                v-model="form.totalRooms"
                :min="1"
                :max="999"
                placeholder="请输入总房间数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房间面积" prop="roomArea">
              <el-input-number
                v-model="form.roomArea"
                :min="0"
                :max="999.99"
                :precision="2"
                placeholder="请输入房间面积"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #999;">平方米</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="楼层信息" prop="roomFloor">
              <el-input v-model="form.roomFloor" placeholder="请输入楼层信息"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大可预订天数" prop="maxBookingDays">
              <el-input-number
                v-model="form.maxBookingDays"
                :min="1"
                :max="365"
                placeholder="请输入最大可预订天数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 特价房源和周末不加价设置 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="特价房源">
              <el-switch
                v-model="form.isSpecialOffer"
                :active-value="1"
                :inactive-value="0"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="form-tip">开启后此房型支持创建特价策略，商户可根据客流量动态调价</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="周末不加价">
              <el-switch
                v-model="form.weekendNoMarkup"
                :active-value="1"
                :inactive-value="0"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="form-tip">开启后周末价格与工作日价格保持一致</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="房间设施" prop="roomFacilities">
          <el-checkbox-group v-model="facilitiesChecked">
            <el-checkbox label="空调">空调</el-checkbox>
            <el-checkbox label="电视">电视</el-checkbox>
            <el-checkbox label="WiFi">WiFi</el-checkbox>
            <el-checkbox label="热水器">热水器</el-checkbox>
            <el-checkbox label="冰箱">冰箱</el-checkbox>
            <el-checkbox label="保险箱">保险箱</el-checkbox>
            <el-checkbox label="吹风机">吹风机</el-checkbox>
            <el-checkbox label="拖鞋">拖鞋</el-checkbox>
            <el-checkbox label="浴袍">浴袍</el-checkbox>
            <el-checkbox label="洗漱用品">洗漱用品</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="房间图片" prop="roomImages">
          <self-upload
            v-model="form.roomImages"
            :multiple="true"
          />
        </el-form-item>

        <el-form-item label="房间描述" prop="roomDescription">
          <el-input
            v-model="form.roomDescription"
            type="textarea"
            :rows="4"
            placeholder="请输入房间描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="9999"
            placeholder="请输入排序"
            style="width: 200px"
          />
          <span style="margin-left: 8px; color: #999;">数值越小越靠前</span>
        </el-form-item>
      </el-form>
    </div>

    <!-- 第二步：价格策略配置 -->
    <div v-if="currentStep === 1 && !isEdit && !formData.id">
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 10px;">房型信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="房型名称">{{ form.roomName }}</el-descriptions-item>
          <el-descriptions-item label="房型类型">{{ form.roomType }}</el-descriptions-item>
          <el-descriptions-item label="床型">{{ form.bedType }}</el-descriptions-item>
          <el-descriptions-item label="最大入住人数">{{ form.maxGuests }}人</el-descriptions-item>
          <el-descriptions-item label="总房间数">{{ form.totalRooms }}间</el-descriptions-item>
          <el-descriptions-item label="基础价格">{{ form.basePrice }}元</el-descriptions-item>
        </el-descriptions>
      </div>

      <h3 style="margin-bottom: 15px;">价格策略配置</h3>
      <el-form ref="priceForm" :model="priceFormData" :rules="priceRules" label-width="120px">
        <div v-for="(strategy, index) in priceStrategies" :key="index"
             style="border: 1px solid #e4e7ed; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
          <h4 style="margin-bottom: 10px; color: #409eff;">{{ strategy.strategyName }}</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="strategy.strategyName + '价格'"
                            :rules="[{ required: true, message: strategy.strategyName + '不能为空', trigger: 'blur' }]">
                <el-input-number
                  v-model="strategy.priceValue"
                  :min="0"
                  :precision="2"
                  placeholder="请输入价格"
                  style="width: 100%"
                >
                  <template slot="append">元</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="优先级">
                <el-input-number
                  v-model="strategy.priority"
                  :min="1"
                  :max="10"
                  disabled
                  style="width: 100%"
                />
                <div style="font-size: 12px; color: #999; margin-top: 5px;">
                  {{ strategy.priorityDesc }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <!-- 编辑或查看模式 -->
      <template v-if="isEdit || formData.id">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="isEdit" type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </template>

      <!-- 新增模式 - 第一步 -->
      <template v-else-if="currentStep === 0">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleNext" :loading="submitLoading">下一步</el-button>
      </template>

      <!-- 新增模式 - 第二步 -->
      <template v-else-if="currentStep === 1">
        <el-button @click="handlePrev">上一步</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">完成</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import {hotelRoomCreateApi, hotelRoomCreateWithPricesApi, hotelRoomUpdateApi} from '@/api/hotel';
import SelfUpload from '@/components/base/selfUpload.vue';

export default {
  name: 'RoomForm',
  components: {
    SelfUpload,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 步骤控制
      currentStep: 0,

      form: {
        id: null,
        roomName: '',
        roomType: '',
        roomFacilities: '',
        roomArea: null,
        roomFloor: '',
        bedType: '',
        maxGuests: 2,
        basePrice: null,
        totalRooms: 1,
        roomImages: '',
        roomDescription: '',
        maxBookingDays: 30,
        status: 1,
        sort: 0,
        isSpecialOffer: 0,
        weekendNoMarkup: 0,
      },
      facilitiesChecked: [],

      // 价格策略数据
      priceStrategies: [
        {
          strategyName: '工作日价格',
          strategyType: 1,
          weekDays: '1,2,3,4,5',
          priceValue: null,
          priority: 1,
          status: 1,
          priorityDesc: '周一至周五适用'
        },
        {
          strategyName: '周末价格',
          strategyType: 2,
          weekDays: '6,7',
          priceValue: null,
          priority: 2,
          status: 1,
          priorityDesc: '周六、周日适用'
        },
        {
          strategyName: '节假日价格',
          strategyType: 3,
          weekDays: '',
          priceValue: null,
          priority: 3,
          status: 1,
          priorityDesc: '法定节假日适用，优先级最高'
        }
      ],

      // 价格策略表单数据对象
      priceFormData: {
        strategies: []
      },

      submitLoading: false,
      rules: {
        roomName: [
          {required: true, message: '房型名称不能为空', trigger: 'blur'},
          {max: 100, message: '房型名称长度不能超过100个字符', trigger: 'blur'},
        ],
        roomType: [
          {required: true, message: '房型类型不能为空', trigger: 'change'},
        ],
        bedType: [
          {required: true, message: '床型不能为空', trigger: 'change'},
        ],
        maxGuests: [
          {required: true, message: '最大入住人数不能为空', trigger: 'blur'},
        ],
        basePrice: [
          {required: true, message: '房型基础价格不能为空', trigger: 'blur'},
        ],
        totalRooms: [
          {required: true, message: '总房间数不能为空', trigger: 'blur'},
        ],
        maxBookingDays: [
          {required: true, message: '最大可预订天数不能为空', trigger: 'blur'},
        ],
        status: [
          {required: true, message: '状态不能为空', trigger: 'change'},
        ],
      },

      // 价格策略验证规则
      priceRules: {},
    };
  },
  watch: {
    visible(val) {
      if (val) {
        // 重置步骤
        this.currentStep = 0;
        this.initForm();
      }
    },
    formData: {
      handler(val) {
        if (val && this.visible) {
          this.initForm();
        }
      },
      deep: true,
      immediate: true,
    },
    facilitiesChecked: {
      handler(val) {
        this.form.roomFacilities = JSON.stringify(val);
      },
      deep: true,
    },
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      if (this.formData.id) {
        this.form = {...this.formData};
        // 解析设施
        try {
          this.facilitiesChecked = this.form.roomFacilities ? JSON.parse(this.form.roomFacilities) : [];
        } catch (e) {
          this.facilitiesChecked = [];
        }
      } else {
        this.form = {
          id: null,
          roomName: '',
          roomType: '',
          roomFacilities: '',
          roomArea: null,
          roomFloor: '',
          bedType: '',
          maxGuests: 2,
          basePrice: null,
          totalRooms: 1,
          roomImages: '',
          roomDescription: '',
          maxBookingDays: 30,
          status: 1,
          sort: 0,
        };
        this.facilitiesChecked = [];
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    /** 下一步 */
    handleNext() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.currentStep = 1;
          // 初始化价格策略的默认值
          this.priceStrategies.forEach(strategy => {
            if (strategy.priceValue === null || strategy.priceValue === 0) {
              strategy.priceValue = this.form.basePrice || 100;
            }
          });
          console.log('价格策略初始化:', this.priceStrategies); // 调试日志
        }
      });
    },

    /** 上一步 */
    handlePrev() {
      this.currentStep = 0;
    },

    /** 提交 */
    handleSubmit() {
      // 编辑模式或查看模式，直接提交房型信息
      if (this.isEdit || this.formData.id) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.submitLoading = true;
            const api = this.form.id ? hotelRoomUpdateApi : hotelRoomCreateApi;
            api(this.form)
              .then(() => {
                this.$modal.msgSuccess(this.form.id ? '修改成功' : '新增成功');
                this.$emit('success');
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        });
        return;
      }

      // 新增模式，需要验证价格策略
      const priceValid = this.validatePriceStrategies();
      if (priceValid) {
        this.submitLoading = true;

        // 构建提交数据
        const submitData = {
          roomData: {...this.form},
          priceStrategies: this.priceStrategies.map(strategy => ({
            strategyName: strategy.strategyName,
            strategyType: strategy.strategyType,
            weekDays: strategy.weekDays,
            priceValue: strategy.priceValue,
            priority: strategy.priority,
            status: strategy.status || 1
          }))
        };

        // 调用新的API接口
        this.submitRoomWithPrices(submitData);
      } else {
        this.$modal.msgError('请完善价格策略信息');
      }
    },

    /** 提交房型和价格策略 */
    async submitRoomWithPrices(data) {
      try {
        console.log('提交数据:', data); // 调试日志
        // 调用新的组合API
        await hotelRoomCreateWithPricesApi(data);
        this.$modal.msgSuccess('房型和价格策略创建成功');
        this.$emit('success');
      } catch (error) {
        console.error('创建失败:', error);
        if (error.response && error.response.data && error.response.data.message) {
          this.$modal.msgError('创建失败：' + error.response.data.message);
        } else {
          this.$modal.msgError('创建失败，请重试');
        }
      } finally {
        this.submitLoading = false;
      }
    },

    /** 验证价格策略 */
    validatePriceStrategies() {
      for (let i = 0; i < this.priceStrategies.length; i++) {
        const strategy = this.priceStrategies[i];
        if (!strategy.priceValue || strategy.priceValue <= 0) {
          this.$modal.msgError(`${strategy.strategyName}不能为空或小于等于0`);
          return false;
        }
      }
      return true;
    },

    /** 关闭 */
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
