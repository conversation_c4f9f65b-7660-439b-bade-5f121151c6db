<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="数据类型：" prop="dataType">
        <el-select v-model.trim="pram.dataType" placeholder="请选择数据类型" clearable style="width: 100%;">
          <el-option v-for="item in dataConfigList" :key="item.key" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据描述：" prop="dataDesc">
        <el-input type="textarea" v-model.trim="pram.dataDesc" placeholder="请输入数据描述"/>
      </el-form-item>
      <el-form-item label="数据内容：" prop="dataContent">
        <Tinymce height="200px" v-model="pram.dataContent" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import {Debounce} from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import * as dataApi from "@/api/foundation/data";

export default {
  components: {Tinymce},
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    dataConfigList() {
      return this.dictData?.dataConfig?.itemList || []
    }
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        dataType: null,
        dataDesc: null,
        dataContent: null
      },
      roleList: [],
      rules: {
        dataType: [{required: true, message: '请选择数据类型', trigger: ['blur', 'change']}]
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      const {id, dataType, dataDesc, dataContent} = this.editData;
      this.pram.id = id;
      this.pram.dataType = dataType;
      this.pram.dataDesc = dataDesc;
      this.pram.dataContent = dataContent;
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(this.pram);
      this.$message.success('创建数据成功');
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(this.pram);
      this.$message.success('更新数据成功');
      this.$emit('hideEditDialog');
    }
  },
};
</script>

<style scoped></style>
