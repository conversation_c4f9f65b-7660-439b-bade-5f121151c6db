<template>
  <div class="content">
    <div class="phoneBox">
      <div class="fontBox" v-html="formValidate.agreement"></div>
    </div>
    <div class="ueditor">
      <Tinymce v-model="formValidate.agreement" :key="keyIndex"></Tinymce>
    </div>
    <el-button class="save-button" type="primary" icon="el-icon-folder-checked" circle @click="submitData"></el-button>
  </div>
</template>

<script>
import Tinymce from "@/components/Tinymce/index.vue";
import { save, update, getDetail } from "@/api/foundation/housekeeperMgmt";

export default {
  name: 'housekeeperMgmt',
  components: {Tinymce},
  data() {
    return {
      dataSource: {},
      formValidate: {
        agreement: '',
      },
      keyIndex: 0,
    }
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    // 获取数据
    getInfo() {
      getDetail().then(res => {
        this.dataSource = res;
        this.formValidate.agreement = res?.hkDesc;
      })
    },

    // 保存数据
    submitData() {
      // 判断输入框内容是否为空
      try {
        if (!this.formValidate.agreement)
          throw new Error('请输入内容');
        // 提交数据到后端
        this.dataSource.hkDesc = this.formValidate.agreement;
        if (this.dataSource.id) {
          update(this.dataSource);
        } else {
          save(this.dataSource);
        }
        this.$message({
          message: '保存成功',
          type: 'success',
        });
      } catch (e) {
        this.$message({
          message: e,
          type: 'warning',
        });
      }
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  img {
    max-width: 100%;
  }

  .phoneBox {
    width: 302px;
    background-image: url('../../../assets/imgs/phoneBox.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    overflow: hidden;
    margin-right: 30px;

    .fontBox {
      margin: 0 auto;
      margin-top: 45px;
      width: 255px;
      height: 450px;
      background: #ffffff;
      border: 1px solid #e2e2e2;
      padding: 10px;
      overflow: hidden;
      overflow-y: auto;

      ::v-deep img {
        width: 100% !important;
      }
    }
  }

  .ueditor {
    flex: 1;

    .font {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 30px;
    }
  }

  .save-button {
    width: 40px;
    height: 40px;
    position: absolute;
    right: 5%;
    bottom: 22%;
  }
}
</style>
