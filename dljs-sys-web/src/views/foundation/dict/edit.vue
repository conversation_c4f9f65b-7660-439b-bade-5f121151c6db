<template>
  <div>
    <el-form ref="pram" :model="pram" label-width="75px" @submit.native.prevent>
      <el-form-item
        label="字典名称"
        prop="dictName"
        :rules="[{ required: true, message: '请填写字典名称', trigger: ['blur', 'change'] }]"
      >
        <el-input v-model.trim="pram.dictName" placeholder="字典名称" />
      </el-form-item>
      <el-form-item
        label="字典编码"
        prop="dictCode"
        :rules="[{ required: true, message: '请填写字典编码', trigger: ['blur', 'change'] }]"
      >
        <el-input v-model.trim="pram.dictCode" placeholder="字典编码" />
      </el-form-item>
      <el-form-item
        label="描述"
        prop="dictDesc"
        :rules="[{ required: false, message: '请填写字典描述'}]"
      >
        <el-input v-model.trim="pram.dictDesc" placeholder="描述" />
      </el-form-item>

    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
   
      >
        {{ isCreate === 0 ? '确定' : '更新' }}</el-button
      >
    </div>
  </div>
</template>

<script>

import * as dictApi from '@/api/dict.js';
import { Debounce } from '@/utils/validate';
export default {
  name: 'roleEdit',
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      pram: {
        dictName: null,
        dictCode: null,
        dictDesc: null,
        id: null,
      },
      menuExpand: false,
      menuNodeAll: false,
      menuOptions: [],
      menuCheckStrictly: true,
      currentNodeId: [],
      defaultProps: {
        children: 'childList',
        label: 'name',
      },
      menuIds: [],
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      const { dictName, dictCode,dictDesc, id } = this.editData;
      this.pram.dictName = dictName;
      this.pram.dictCode = dictCode;
      this.pram.dictDesc = dictDesc;
      this.pram.id = id;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
      });
      dictApi.getInfo(id).then((res) => {
        loading.close();
      });
    },
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        if (!valid) return;
        if (this.isCreate === 0) {
          this.handlerSave();
        } else {
          this.handlerEdit();
        }
      });
    }),
    handlerSave() {
      dictApi.addDict(this.pram).then((data) => {
        this.$message.success('新增字典成功');
        this.$emit('hideEditDialog');
      });
    },
    handlerEdit() {
      dictApi.updateDict(this.pram).then((data) => {
        this.$message.success('更新字典成功');
        this.$emit('hideEditDialog');
      });
    },
    rulesSelect(selectKeys) {
      this.pram.rules = selectKeys;
    },
  },
};
</script>

<style scoped></style>
