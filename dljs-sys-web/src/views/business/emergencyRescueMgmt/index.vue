<template>
  <div class="divBox">
    <el-card
      class="box-card"
      :body-style="{ padding: 0 }"
      :bordered="false"
      shadow="never"
      v-hasPermi="['platform:admin:list']"
    >
      <div class="padding-add">
        <el-form ref="pram" :model="listPram" :rules="rules" label-width="100px" @submit.native.prevent>
          <el-form-item label="id：" prop="id" v-show="false">
            <el-input v-model.trim="listPram.id" placeholder="id"/>
          </el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="救援电话名称：" prop="erName">
                <el-input v-model.trim="listPram.erName" placeholder="请输入救援电话名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="救援电话：" prop="erPhone">
                <el-input v-model.trim="listPram.erPhone" placeholder="请输入救援电话"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="应急疏散线路：" prop="erLineImg">
            <div class="upLoadPicBox" @click="modalPicTap(false)">
              <div v-if="listPram.erLineImg" class="pictrue">
                <img :src="getImgUrl(listPram.erLineImg)[0]" alt=""/>
              </div>
              <div v-else class="upLoad">
                <i class="el-icon-camera cameraIconfont"/>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card
      class="box-card mt14"
      :body-style="{ height: '450px', padding: '15px' }"
      :bordered="false"
      shadow="never"
      v-hasPermi="['platform:admin:list']"
    >
      <el-button size="mini" style="margin-bottom: 15px" type="primary">紧急救援说明</el-button>
      <Tinymce height="200px" v-model="listPram.erLineDesc" :key="keyIndex"></Tinymce>
    </el-card>

    <el-card class="box-card mt14 box-card-table" :body-style="{ height: 'calc(100vh - 500px)', padding: '20px' }" shadow="never" :bordered="false">
      <el-button size="mini" type="primary" @click="handlerOpenEdit(0)">添加安全注意事项</el-button>
      <el-table v-loading="listLoading" class="operation mt20 table-info" :data="listData" size="small" :key="isUpdate" height="340">
        <el-table-column label="序号" prop="index"/>
        <el-table-column label="名称" prop="name"/>
        <el-table-column label="内容" prop="content"/>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <template v-if="scope.row.isDel">
              <span>-</span>
            </template>
            <template v-else>
              <el-button
                :disabled="scope.row.roles === '1'"
                type="text"
                size="mini"
                @click="handlerOpenEdit(1, scope.row)"
                v-hasPermi="['platform:admin:update', 'platform:admin:info']"
              >编辑
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button
                type="text"
                size="mini"
                :disabled="scope.row.roles === '1'"
                @click="handlerOpenDel(scope.row)"
                v-hasPermi="['platform:admin:delete']"
              >删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card
        class="box-card mt14"
        :body-style="{ height: '40px', padding: 0, textAlign: 'center', lineHeight: 3 }"
        :bordered="false"
        shadow="never"
        v-hasPermi="['platform:admin:list']"
    >
      <el-button
          type="primary"
          size="mini"
          @click="handlerSubmit('pram')"
          v-hasPermi="['platform:admin:update', 'platform:admin:info']"
      >保存
      </el-button>
    </el-card>
    <!--编辑-->
    <el-dialog
      top="40px"
      :visible.sync="editDialogConfig.visible"
      :title="editDialogConfig.isCreate === 0 ?  '创建安全注意事项' : '编辑安全注意事项'"
      destroy-on-close
      :close-on-click-modal="false"
      width="700px"
      class="dialog-bottom"
    >
      <edit
        v-if="editDialogConfig.visible"
        :is-create="editDialogConfig.isCreate"
        :edit-data="editDialogConfig.editData"
        :dict-data="{}"
        @hideEditDialog="hideEditDialog"
      />
    </el-dialog>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import {emergency as apiConf} from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import edit from './edit';
import Tinymce from "@/components/Tinymce/index.vue";
import {getImgUrl} from "@/utils/imgUtil";
import {Debounce} from '@/utils/validate';

export default {
  // name: "index"
  components: {edit, Tinymce},
  computed: {
    apiConfig() {
      return apiConf || {name: '', path: ''}
    }
  },
  data() {
    return {
      constants: this.$constants,
      listData: [],
      listPram: {
        id: null,
        erName: null,
        erPhone: null,
        erLineImg: null,
        erLineDesc:  null,
        erSecurityItem: null,
      },
      realName: '',
      roleList: [],
      rules: {
        erName: [{required: true, message: '请输入救援电话名称', trigger: ['blur', 'change']}],
        erPhone: [{required: true, message: '请输入救援电话', trigger: ['blur', 'change']}],
        erLineImg: [{required: true, message: '请上传应急疏散线路', trigger: ['blur', 'change']}],
        erLineDesc: [{required: true, message: '请输入紧急救援说明', trigger: ['blur', 'change']}],
        erSecurityItem: [{required: true, message: '请添加安全事项', trigger: ['blur', 'change']}],
      },
      menuList: [],
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {},
      },
      listLoading: false,
      keyIndex: 0,
      isCreate: 1, // 如果初始话没有获取到数据，则为新建，否则为编辑
      isUpdate: Math.random(),
    };
  },
  mounted() {
    this.handleGetAdminList();
  },
  methods: {
    getImgUrl,
    async handleGetAdminList() {
      this.listLoading = true;
      // 查询list
      const data = await dataApi.info(apiConf.path, '');
      if (data) {
        this.listPram = data;
        this.isCreate = 2;
      }
      if (data && data.erSecurityItem) this.listData = JSON.parse(data.erSecurityItem);
      this.listLoading = false;
    },
    handlerOpenDel(rowData) {
      this.$modalSure('确认删除当前数据').then(async () => {
        try {
          const id = rowData.id;
          //从this.listData对象中移除id相等的对象
          this.listData = this.listData.filter((item) => item.id !== id);
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    },
    // 点击商品图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
          function (img) {
            if (!img) return;
            _this.listPram.erLineImg = img[0].sattDir;
          },
          multiple,
          'store',
      );
    },
    handlerOpenEdit(isCreate, editDate) {
      this.editDialogConfig.editData = editDate;
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog(data) {
      if (data) {
        if (data.type === 2) {
          // 替换table对象的信息
          const id = data.id;
          for (let da of this.listData) {
            if (da.id === id) {
              this.$set(da, 'index', data.index);
              this.$set(da, 'name', data.name);
              this.$set(da, 'content', data.content);
            }
          }
        } else {
          this.listData.push(data);
        }
      }
      this.editDialogConfig.visible = false;
      this.isUpdate = Math.random();
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      let listPram = { ...this.listPram };
      let listData = this.listData;
      let isCreate = this.isCreate;
      this.$refs[form].validate(async (valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (!listPram.erLineDesc) throw new Error('请输入紧急救援说明')
          if (!listData) throw new Error('请添加安全事项')
          listPram.erSecurityItem = JSON.stringify(listData);
          if (isCreate === 1){
            await dataApi.save(apiConf.path, listPram);
          } else {
            await dataApi.update(apiConf.path, listPram);
          }
          this.$message.success('保存成功');
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
  },
};
</script>

<style scoped lang="scss">
.box-card-table {
  height: 400px !important;
  .el-card__body{
    height: 400px !important;
  }
}
.el-table {
  overflow-y: auto;
}
</style>
