<template>
  <div class="divBox relative">
    <!-- 搜索条件 -->
    <el-card
      v-if="$auth.hasPermi('admin:hotel:room:list')"
      :bordered="false"
      shadow="never"
      class="ivu-mt"
      :body-style="{ padding: 0 }"
    >
      <div class="padding-add">
        <el-form inline size="small" label-position="right" @submit.native.prevent>
          <el-form-item label="房型名称：">
            <el-input
              v-model.trim="searchForm.roomName"
              placeholder="请输入房型名称"
              class="form_content_width"
              size="small"
              @keyup.enter.native="handleSearch"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="房型类型：">
            <el-input
              v-model.trim="searchForm.roomType"
              placeholder="请输入房型类型"
              class="form_content_width"
              size="small"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="searchForm.status"
              clearable
              size="small"
              placeholder="请选择状态"
              class="selWidth"
            >
              <el-option label="启用" :value="1"/>
              <el-option label="禁用" :value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item label="床型：">
            <el-input
              v-model.trim="searchForm.bedType"
              placeholder="请输入床型"
              class="form_content_width"
              size="small"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="特价房源：">
            <el-select
              v-model="searchForm.isSpecialOffer"
              clearable
              size="small"
              placeholder="请选择"
              class="selWidth"
            >
              <el-option label="是" :value="1"/>
              <el-option label="否" :value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item label="周末不加价：">
            <el-select
              v-model="searchForm.weekendNoMarkup"
              clearable
              size="small"
              placeholder="请选择"
              class="selWidth"
            >
              <el-option label="是" :value="1"/>
              <el-option label="否" :value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮和表格 -->
    <el-card class="box-card mt14" :body-style="{ padding: '0 20px 20px' }" shadow="never" :bordered="false">
      <div class="clearfix mb20">
        <el-button size="small" type="primary" v-hasPermi="['admin:hotel:room:save']" @click="handleAdd">
          添加房型
        </el-button>

        <!-- 批量操作按钮 -->
        <el-dropdown v-if="selectedRows.length > 0" style="margin-left: 10px;">
          <el-button size="small" type="success">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleBatchSpecialOffer(1)">
              <i class="el-icon-star-on"></i> 设为特价房源
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleBatchSpecialOffer(0)">
              <i class="el-icon-star-off"></i> 取消特价
            </el-dropdown-item>
            <el-dropdown-item divided @click.native="handleBatchWeekendNoMarkup(1)">
              <i class="el-icon-time"></i> 开启周末不加价
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleBatchWeekendNoMarkup(0)">
              <i class="el-icon-time"></i> 关闭周末不加价
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <span v-if="selectedRows.length > 0" style="margin-left: 10px; color: #909399; font-size: 12px;">
          已选择 {{ selectedRows.length }} 项
        </span>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column prop="roomName" label="房型名称" min-width="120"/>
        <el-table-column prop="roomType" label="房型类型" min-width="100"/>
        <el-table-column prop="bedType" label="床型" min-width="100"/>
        <el-table-column prop="maxGuests" label="最大入住人数" width="120" align="center"/>
        <el-table-column prop="basePrice" label="基础价格" width="100" align="center">
          <template slot-scope="scope">
            ¥{{ scope.row.basePrice }}
          </template>
        </el-table-column>
        <el-table-column prop="totalRooms" label="总房间数" width="100" align="center"/>
        <el-table-column prop="roomArea" label="房间面积" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.roomArea }}㎡
          </template>
        </el-table-column>

        <!-- 特价房源列 -->
        <el-table-column label="特价房源" width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isSpecialOffer === 1" type="danger" size="mini">特价</el-tag>
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>

        <!-- 周末不加价列 -->
        <el-table-column label="周末不加价" width="110" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.weekendNoMarkup === 1" type="success" size="mini">周末不加价</el-tag>
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>

        <!--        <el-table-column prop="priceStrategyCount" label="价格策略数" width="120" align="center" />-->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-hasPermi="['admin:hotel:room:status']"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="190" align="center" fixed="right">
          <template slot-scope="scope">
            <a @click="handleView(scope.row)" v-hasPermi="['admin:hotel:room:info']">查看</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleEdit(scope.row)" v-hasPermi="['admin:hotel:room:update']">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleStock(scope.row)" v-hasPermi="['admin:hotel:room:stock']">库存</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleDelete(scope.row)" style="color: #f56c6c" v-hasPermi="['admin:hotel:room:delete']">删除</a>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination
          v-show="total > 0"
          background
          :page-sizes="[10, 20, 30, 40]"
          :page-size="queryParams.limit"
          :current-page="queryParams.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <room-form
      v-if="formVisible"
      :visible="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @close="handleFormClose"
      @success="handleFormSuccess"
    />

    <!-- 库存修改对话框 -->
    <el-dialog title="修改库存" :visible.sync="stockVisible" width="400px" append-to-body>
      <el-form :model="stockForm" label-width="100px">
        <el-form-item label="房型名称：">
          <span>{{ stockForm.roomName }}</span>
        </el-form-item>
        <el-form-item label="当前库存：">
          <span>{{ stockForm.currentStock }}</span>
        </el-form-item>
        <el-form-item label="新库存：" required>
          <el-input-number
            v-model="stockForm.newStock"
            :min="0"
            :max="999"
            placeholder="请输入新库存"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stockVisible = false">取消</el-button>
        <el-button type="primary" @click="handleStockSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  hotelRoomDeleteApi,
  hotelRoomListApi,
  hotelRoomStatusApi,
  hotelRoomStockApi,
  hotelRoomBatchSpecialOfferApi,
  hotelRoomBatchWeekendApi
} from '@/api/hotel';
import RoomForm from './components/RoomForm.vue';
import {parseTime} from '@/utils/index';

export default {
  name: 'HotelRoom',
  components: {
    RoomForm,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      queryParams: {
        page: 1,
        limit: 10,
      },
      searchForm: {
        roomName: '',
        roomType: '',
        status: null,
        bedType: '',
        maxGuests: null,
        isSpecialOffer: null,
        weekendNoMarkup: null,
      },
      selectedRows: [],
      formVisible: false,
      formData: {},
      isEdit: false,
      stockVisible: false,
      stockForm: {
        id: null,
        roomName: '',
        currentStock: 0,
        newStock: 0,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,

    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        ...this.searchForm,
      };
      hotelRoomListApi(params)
        .then((response) => {
          // 修复数据解析逻辑 - axios拦截器已经返回了res.data，所以response就是后端的data部分
          console.log('API响应数据:', response);
          if (response && response.list) {
            this.tableData = response.list;
            this.total = response.total || 0;
            console.log('设置数据成功:', this.tableData, this.total);
          } else {
            // 如果数据结构不符合预期，清空数据并记录错误
            console.warn('酒店房间列表数据结构异常:', response);
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          console.error('获取酒店房间列表失败:', error);
          this.tableData = [];
          this.total = 0;
          this.$message.error('获取房间列表失败，请稍后重试');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /** 搜索 */
    handleSearch() {
      this.queryParams.page = 1;
      this.getList();
    },

    /** 重置 */
    handleReset() {
      this.searchForm = {
        roomName: '',
        roomType: '',
        status: null,
        bedType: '',
        maxGuests: null,
        isSpecialOffer: null,
        weekendNoMarkup: null,
      };
      this.handleSearch();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    /** 新增 */
    handleAdd() {
      this.formData = {};
      this.isEdit = false;
      this.formVisible = true;
    },

    /** 查看 */
    handleView(row) {
      this.formData = {...row};
      this.isEdit = false;
      this.formVisible = true;
    },

    /** 编辑 */
    handleEdit(row) {
      this.formData = {...row};
      this.isEdit = true;
      this.formVisible = true;
    },

    /** 删除 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除房型"' + row.roomName + '"？').then(() => {
        return hotelRoomDeleteApi(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      });
    },

    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.status === 1 ? '启用' : '禁用';
      this.$modal.confirm('确认要"' + text + '""' + row.roomName + '"房型吗？').then(() => {
        return hotelRoomStatusApi(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + '成功');
      }).catch(() => {
        row.status = row.status === 0 ? 1 : 0;
      });
    },

    /** 库存修改 */
    handleStock(row) {
      this.stockForm = {
        id: row.id,
        roomName: row.roomName,
        currentStock: row.totalRooms,
        newStock: row.totalRooms,
      };
      this.stockVisible = true;
    },

    /** 库存提交 */
    handleStockSubmit() {
      hotelRoomStockApi(this.stockForm.id, this.stockForm.newStock).then(() => {
        this.$modal.msgSuccess('库存修改成功');
        this.stockVisible = false;
        this.getList();
      });
    },

    /** 表单关闭 */
    handleFormClose() {
      this.formVisible = false;
    },

    /** 表单提交成功 */
    handleFormSuccess() {
      this.formVisible = false;
      this.getList();
    },

    /** 分页大小改变 */
    handleSizeChange(val) {
      this.queryParams.limit = val;
      this.queryParams.page = 1;
      this.getList();
    },

    /** 当前页改变 */
    handleCurrentChange(val) {
      this.queryParams.page = val;
      this.getList();
    },

    /** 批量设置特价房源 */
    handleBatchSpecialOffer(isSpecialOffer) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要操作的房型');
        return;
      }

      const action = isSpecialOffer === 1 ? '设为特价房源' : '取消特价';
      this.$confirm(`确定要将选中的 ${this.selectedRows.length} 个房型${action}吗？`, '批量操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const roomIds = this.selectedRows.map(row => row.id);
        hotelRoomBatchSpecialOfferApi({
          roomIds: roomIds,
          isSpecialOffer: isSpecialOffer
        }).then(() => {
          this.$message.success(`${action}成功`);
          this.getList(); // 刷新列表
          this.selectedRows = []; // 清空选择
        }).catch(() => {
          this.$message.error(`${action}失败`);
        });
      });
    },

    /** 批量设置周末不加价 */
    handleBatchWeekendNoMarkup(weekendNoMarkup) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要操作的房型');
        return;
      }

      const action = weekendNoMarkup === 1 ? '开启周末不加价' : '关闭周末不加价';
      this.$confirm(`确定要将选中的 ${this.selectedRows.length} 个房型${action}吗？`, '批量操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const roomIds = this.selectedRows.map(row => row.id);
        hotelRoomBatchWeekendApi({
          roomIds: roomIds,
          weekendNoMarkup: weekendNoMarkup
        }).then(() => {
          this.$message.success(`${action}成功`);
          this.getList(); // 刷新列表
          this.selectedRows = []; // 清空选择
        }).catch(() => {
          this.$message.error(`${action}失败`);
        });
      });
    },
  },
};
</script>

<style scoped>
.form_content_width {
  width: 200px;
}

.selWidth {
  width: 120px;
}
</style>
